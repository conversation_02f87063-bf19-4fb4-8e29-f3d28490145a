pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*
Description: Magula style for highligh.js
Author: <PERSON><PERSON><PERSON> <<EMAIL>>
Website: http://rukeba.com/
Version: 1.0
Date: 2009-01-03
Music: Aphex Twin / Xtal
*/
.hljs {
  background-color: #f4f4f4;
  color: black
}
.hljs-subst {
  color: black
}
.hljs-string,
.hljs-title,
.hljs-symbol,
.hljs-bullet,
.hljs-attribute,
.hljs-addition,
.hljs-variable,
.hljs-template-tag,
.hljs-template-variable {
  color: #050
}
.hljs-comment,
.hljs-quote {
  color: #777
}
.hljs-number,
.hljs-regexp,
.hljs-literal,
.hljs-type,
.hljs-link {
  color: #800
}
.hljs-deletion,
.hljs-meta {
  color: #00e
}
.hljs-keyword,
.hljs-selector-tag,
.hljs-doctag,
.hljs-title,
.hljs-section,
.hljs-built_in,
.hljs-tag,
.hljs-name {
  font-weight: bold;
  color: navy
}
.hljs-emphasis {
  font-style: italic
}
.hljs-strong {
  font-weight: bold
}
'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { RichTextEditor } from '@/components/editor/rich-text-editor'
import { Save, Eye, Globe, Clock, Tag, Folder, Image } from 'lucide-react'

interface Category {
  id: string
  name: string
  slug: string
}

interface Tag {
  id: string
  name: string
  slug: string
}

export default function WritePage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  const [article, setArticle] = useState({
    title: '',
    subtitle: '',
    content: '',
    categoryId: '',
    tags: [] as string[],
    featuredImageUrl: '',
    featuredImageAlt: '',
    status: 'DRAFT' as 'DRAFT' | 'PUBLISHED',
    metaDescription: '',
    allowComments: true,
  })

  const [categories, setCategories] = useState<Category[]>([])
  const [availableTags, setAvailableTags] = useState<Tag[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin?callbackUrl=/write')
    }
  }, [status, router])

  // Load categories and tags
  useEffect(() => {
    const loadData = async () => {
      try {
        const [categoriesRes, tagsRes] = await Promise.all([
          fetch('/api/categories'),
          fetch('/api/tags')
        ])

        if (categoriesRes.ok) {
          const categoriesData = await categoriesRes.json()
          setCategories(categoriesData)
        }

        if (tagsRes.ok) {
          const tagsData = await tagsRes.json()
          setAvailableTags(tagsData)
        }
      } catch (error) {
        console.error('Error loading data:', error)
      }
    }

    if (session) {
      loadData()
    }
  }, [session])

  // Auto-save functionality
  useEffect(() => {
    if (!article.title && !article.content) return

    const autoSave = setTimeout(() => {
      handleSave('DRAFT')
    }, 30000) // Auto-save every 30 seconds

    return () => clearTimeout(autoSave)
  }, [article.title, article.content])

  const handleSave = async (status: 'DRAFT' | 'PUBLISHED' = 'DRAFT') => {
    if (!session || !article.title.trim()) return

    setIsSaving(true)
    try {
      const response = await fetch('/api/articles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...article,
          status,
        }),
      })

      if (response.ok) {
        const savedArticle = await response.json()
        setLastSaved(new Date())
        
        if (status === 'PUBLISHED') {
          router.push(`/articles/${savedArticle.slug}`)
        }
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to save article')
      }
    } catch (error) {
      console.error('Error saving article:', error)
      alert('Failed to save article')
    } finally {
      setIsSaving(false)
    }
  }

  const handleTagToggle = (tagId: string) => {
    setArticle(prev => ({
      ...prev,
      tags: prev.tags.includes(tagId)
        ? prev.tags.filter(id => id !== tagId)
        : [...prev.tags, tagId]
    }))
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto py-8 px-4">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold text-gray-900">Write Article</h1>
            <div className="flex items-center gap-3">
              {lastSaved && (
                <span className="text-sm text-gray-500">
                  <Clock className="h-4 w-4 inline mr-1" />
                  Saved {lastSaved.toLocaleTimeString()}
                </span>
              )}
              <button
                onClick={() => handleSave('DRAFT')}
                disabled={isSaving}
                className="flex items-center gap-2 px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
              >
                <Save className="h-4 w-4" />
                {isSaving ? 'Saving...' : 'Save Draft'}
              </button>
              <button
                onClick={() => handleSave('PUBLISHED')}
                disabled={isSaving || !article.title.trim()}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                <Globe className="h-4 w-4" />
                Publish
              </button>
            </div>
          </div>

          {/* Title */}
          <div className="mb-4">
            <input
              type="text"
              placeholder="Article title..."
              value={article.title}
              onChange={(e) => setArticle({ ...article, title: e.target.value })}
              className="w-full text-3xl font-bold border-none outline-none placeholder-gray-400 resize-none"
            />
          </div>

          {/* Subtitle */}
          <div className="mb-6">
            <input
              type="text"
              placeholder="Add a subtitle (optional)..."
              value={article.subtitle}
              onChange={(e) => setArticle({ ...article, subtitle: e.target.value })}
              className="w-full text-xl text-gray-600 border-none outline-none placeholder-gray-400"
            />
          </div>

          {/* Featured Image */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Image className="h-4 w-4 inline mr-1" />
              Featured Image
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <input
                type="url"
                placeholder="Featured image URL..."
                value={article.featuredImageUrl}
                onChange={(e) => setArticle({ ...article, featuredImageUrl: e.target.value })}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <input
                type="text"
                placeholder="Alt text for accessibility..."
                value={article.featuredImageAlt}
                onChange={(e) => setArticle({ ...article, featuredImageAlt: e.target.value })}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            {article.featuredImageUrl && (
              <div className="mt-2">
                <img
                  src={article.featuredImageUrl}
                  alt={article.featuredImageAlt}
                  className="h-32 w-full object-cover rounded-md"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none'
                  }}
                />
              </div>
            )}
          </div>

          {/* Category and Tags */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {/* Category */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Folder className="h-4 w-4 inline mr-1" />
                Category
              </label>
              <select
                value={article.categoryId}
                onChange={(e) => setArticle({ ...article, categoryId: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select a category</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Tags */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Tag className="h-4 w-4 inline mr-1" />
                Tags
              </label>
              <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                {availableTags.map((tag) => (
                  <button
                    key={tag.id}
                    type="button"
                    onClick={() => handleTagToggle(tag.id)}
                    className={`px-3 py-1 text-sm rounded-full border transition-colors ${
                      article.tags.includes(tag.id)
                        ? 'bg-blue-100 border-blue-300 text-blue-800'
                        : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {tag.name}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* SEO */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Meta Description (SEO)
            </label>
            <textarea
              placeholder="Brief description for search engines..."
              value={article.metaDescription}
              onChange={(e) => setArticle({ ...article, metaDescription: e.target.value })}
              rows={2}
              maxLength={160}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
            />
            <p className="text-xs text-gray-500 mt-1">
              {article.metaDescription.length}/160 characters
            </p>
          </div>
        </div>

        {/* Editor */}
        <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
          <RichTextEditor
            content={article.content}
            onChange={(content) => setArticle({ ...article, content })}
            placeholder="Tell your story..."
          />
        </div>
      </div>
    </div>
  )
}

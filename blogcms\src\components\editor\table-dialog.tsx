'use client'

import { useState } from 'react'
import { X, Table } from 'lucide-react'

interface TableDialogProps {
  isOpen: boolean
  onClose: () => void
  onInsert: (rows: number, cols: number) => void
}

export function TableDialog({ isOpen, onClose, onInsert }: TableDialogProps) {
  const [rows, setRows] = useState(3)
  const [cols, setCols] = useState(3)

  const handleInsert = () => {
    onInsert(rows, cols)
    onClose()
  }

  const renderTablePreview = () => {
    const previewRows = []
    
    // Header row
    previewRows.push(
      <tr key="header" className="bg-gray-100">
        {Array.from({ length: cols }, (_, colIndex) => (
          <th key={colIndex} className="border border-gray-300 p-2 text-sm font-semibold">
            Header {colIndex + 1}
          </th>
        ))}
      </tr>
    )
    
    // Data rows
    for (let rowIndex = 1; rowIndex < rows; rowIndex++) {
      previewRows.push(
        <tr key={rowIndex}>
          {Array.from({ length: cols }, (_, colIndex) => (
            <td key={colIndex} className="border border-gray-300 p-2 text-sm">
              Cell {rowIndex}-{colIndex + 1}
            </td>
          ))}
        </tr>
      )
    }
    
    return previewRows
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Table className="h-5 w-5" />
            Insert Table
          </h3>
          <button
            type="button"
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="space-y-6">
          {/* Table Configuration */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Number of Rows
              </label>
              <input
                type="number"
                min="2"
                max="20"
                value={rows}
                onChange={(e) => setRows(Math.max(2, Math.min(20, parseInt(e.target.value) || 2)))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">Includes header row</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Number of Columns
              </label>
              <input
                type="number"
                min="1"
                max="10"
                value={cols}
                onChange={(e) => setCols(Math.max(1, Math.min(10, parseInt(e.target.value) || 1)))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* Table Preview */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Preview</h4>
            <div className="border rounded-lg overflow-hidden">
              <table className="w-full border-collapse">
                <tbody>
                  {renderTablePreview()}
                </tbody>
              </table>
            </div>
          </div>

          {/* Quick Size Selector */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Quick Select</h4>
            <div className="grid grid-cols-4 gap-2">
              <button
                type="button"
                onClick={() => { setRows(3); setCols(3) }}
                className="p-2 border border-gray-300 rounded hover:border-blue-500 hover:bg-blue-50 text-sm"
              >
                3×3
              </button>
              <button
                type="button"
                onClick={() => { setRows(4); setCols(4) }}
                className="p-2 border border-gray-300 rounded hover:border-blue-500 hover:bg-blue-50 text-sm"
              >
                4×4
              </button>
              <button
                type="button"
                onClick={() => { setRows(5); setCols(3) }}
                className="p-2 border border-gray-300 rounded hover:border-blue-500 hover:bg-blue-50 text-sm"
              >
                5×3
              </button>
              <button
                type="button"
                onClick={() => { setRows(3); setCols(5) }}
                className="p-2 border border-gray-300 rounded hover:border-blue-500 hover:bg-blue-50 text-sm"
              >
                3×5
              </button>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleInsert}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Insert Table
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

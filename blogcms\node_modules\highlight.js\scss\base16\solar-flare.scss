pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Solar Flare
  Author: <PERSON> (https://chuck.harmston.ch)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme solar-flare
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #18262F  Default Background
base01  #222E38  Lighter Background (Used for status bars, line number and folding marks)
base02  #586875  Selection Background
base03  #667581  Comments, Invisibles, Line Highlighting
base04  #85939E  Dark Foreground (Used for status bars)
base05  #A6AFB8  Default Foreground, Caret, Delimiters, Operators
base06  #E8E9ED  Light Foreground (Not often used)
base07  #F5F7FA  Light Background (Not often used)
base08  #EF5253  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #E66B2B  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #E4B51C  Classes, Markup Bold, Search Text Background
base0B  #7CC844  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #52CBB0  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #33B5E1  Functions, Methods, Attribute IDs, Headings
base0E  #A363D5  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #D73C9A  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #A6AFB8;
  background: #18262F
}
.hljs::selection,
.hljs ::selection {
  background-color: #586875;
  color: #A6AFB8
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #667581 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #667581
}
/* base04 - #85939E -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #85939E
}
/* base05 - #A6AFB8 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #A6AFB8
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #EF5253
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #E66B2B
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #E4B51C
}
.hljs-strong {
  font-weight: bold;
  color: #E4B51C
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #7CC844
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #52CBB0
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #33B5E1
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #A363D5
}
.hljs-emphasis {
  color: #A363D5;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #D73C9A
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}
'use client'

import { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { X, Upload, Link, Image, Video, FileText } from 'lucide-react'

interface MediaUploadDialogProps {
  isOpen: boolean
  onClose: () => void
  onInsert: (url: string, type: 'image' | 'video' | 'pdf') => void
}

export function MediaUploadDialog({ isOpen, onClose, onInsert }: MediaUploadDialogProps) {
  const [activeTab, setActiveTab] = useState<'upload' | 'url'>('upload')
  const [url, setUrl] = useState('')
  const [uploading, setUploading] = useState(false)

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (!file) return

    setUploading(true)
    try {
      // In a real app, you would upload to your storage service (Cloudinary, AWS S3, etc.)
      // For now, we'll create a local URL for demonstration
      const reader = new FileReader()
      reader.onload = () => {
        const result = reader.result as string
        let type: 'image' | 'video' | 'pdf' = 'image'
        
        if (file.type.startsWith('video/')) {
          type = 'video'
        } else if (file.type === 'application/pdf') {
          type = 'pdf'
        }
        
        onInsert(result, type)
        onClose()
      }
      reader.readAsDataURL(file)
    } catch (error) {
      console.error('Upload failed:', error)
    } finally {
      setUploading(false)
    }
  }, [onInsert, onClose])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg'],
      'video/*': ['.mp4', '.webm', '.ogg'],
      'application/pdf': ['.pdf'],
    },
    maxFiles: 1,
  })

  const handleUrlInsert = () => {
    if (!url.trim()) return
    
    let type: 'image' | 'video' | 'pdf' = 'image'
    
    if (url.includes('youtube.com') || url.includes('youtu.be') || url.includes('vimeo.com')) {
      type = 'video'
    } else if (url.endsWith('.pdf')) {
      type = 'pdf'
    }
    
    onInsert(url, type)
    setUrl('')
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Insert Media</h3>
          <button
            type="button"
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b mb-4">
          <button
            type="button"
            onClick={() => setActiveTab('upload')}
            className={`px-4 py-2 font-medium ${
              activeTab === 'upload'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            Upload
          </button>
          <button
            type="button"
            onClick={() => setActiveTab('url')}
            className={`px-4 py-2 font-medium ${
              activeTab === 'url'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            URL
          </button>
        </div>

        {activeTab === 'upload' && (
          <div>
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                isDragActive
                  ? 'border-blue-400 bg-blue-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
            >
              <input {...getInputProps()} />
              <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p className="text-gray-600 mb-2">
                {isDragActive
                  ? 'Drop the file here...'
                  : 'Drag & drop a file here, or click to select'}
              </p>
              <p className="text-sm text-gray-500">
                Supports: Images (PNG, JPG, GIF, WebP), Videos (MP4, WebM), PDFs
              </p>
            </div>
            
            {uploading && (
              <div className="mt-4 text-center">
                <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <p className="mt-2 text-sm text-gray-600">Uploading...</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'url' && (
          <div>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Media URL
                </label>
                <input
                  type="url"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                  placeholder="https://example.com/image.jpg"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div className="text-sm text-gray-600">
                <p className="font-medium mb-2">Supported URLs:</p>
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <Image className="h-4 w-4" />
                    <span>Direct image links (.jpg, .png, .gif, etc.)</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Video className="h-4 w-4" />
                    <span>YouTube, Vimeo, or direct video links</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    <span>PDF documents</span>
                  </div>
                </div>
              </div>
              
              <button
                type="button"
                onClick={handleUrlInsert}
                disabled={!url.trim()}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                Insert Media
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

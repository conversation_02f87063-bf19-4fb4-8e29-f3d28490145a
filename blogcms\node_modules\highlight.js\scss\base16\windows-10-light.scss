pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Windows 10 Light
  Author: <PERSON> (https://github.com/C-Fergus)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme windows-10-light
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #f2f2f2  Default Background
base01  #e5e5e5  Lighter Background (Used for status bars, line number and folding marks)
base02  #d9d9d9  Selection Background
base03  #cccccc  Comments, Invisibles, Line Highlighting
base04  #ababab  Dark Foreground (Used for status bars)
base05  #767676  Default Foreground, Caret, Delimiters, Operators
base06  #414141  Light Foreground (Not often used)
base07  #0c0c0c  Light Background (Not often used)
base08  #c50f1f  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #f9f1a5  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #c19c00  Classes, Markup Bold, Search Text Background
base0B  #13a10e  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #3a96dd  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #0037da  Functions, Methods, Attribute IDs, Headings
base0E  #881798  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #16c60c  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #767676;
  background: #f2f2f2
}
.hljs::selection,
.hljs ::selection {
  background-color: #d9d9d9;
  color: #767676
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #cccccc -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #cccccc
}
/* base04 - #ababab -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #ababab
}
/* base05 - #767676 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #767676
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #c50f1f
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #f9f1a5
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #c19c00
}
.hljs-strong {
  font-weight: bold;
  color: #c19c00
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #13a10e
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #3a96dd
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #0037da
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #881798
}
.hljs-emphasis {
  color: #881798;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #16c60c
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}
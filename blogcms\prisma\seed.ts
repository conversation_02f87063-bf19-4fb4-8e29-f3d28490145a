import { PrismaClient } from '../src/generated/prisma'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create categories
  const categories = await Promise.all([
    prisma.category.upsert({
      where: { slug: 'technology' },
      update: {},
      create: {
        name: 'Technology',
        slug: 'technology',
        description: 'Latest in tech, programming, and innovation',
        color: '#3B82F6'
      }
    }),
    prisma.category.upsert({
      where: { slug: 'design' },
      update: {},
      create: {
        name: 'Design',
        slug: 'design',
        description: 'UI/UX, graphic design, and creative inspiration',
        color: '#8B5CF6'
      }
    }),
    prisma.category.upsert({
      where: { slug: 'business' },
      update: {},
      create: {
        name: 'Business',
        slug: 'business',
        description: 'Entrepreneurship, startups, and business insights',
        color: '#10B981'
      }
    }),
    prisma.category.upsert({
      where: { slug: 'lifestyle' },
      update: {},
      create: {
        name: 'Lifestyle',
        slug: 'lifestyle',
        description: 'Health, wellness, and personal development',
        color: '#F59E0B'
      }
    })
  ])

  // Create tags
  const tags = await Promise.all([
    prisma.tag.upsert({
      where: { slug: 'react' },
      update: {},
      create: { name: 'React', slug: 'react' }
    }),
    prisma.tag.upsert({
      where: { slug: 'nextjs' },
      update: {},
      create: { name: 'Next.js', slug: 'nextjs' }
    }),
    prisma.tag.upsert({
      where: { slug: 'typescript' },
      update: {},
      create: { name: 'TypeScript', slug: 'typescript' }
    }),
    prisma.tag.upsert({
      where: { slug: 'javascript' },
      update: {},
      create: { name: 'JavaScript', slug: 'javascript' }
    }),
    prisma.tag.upsert({
      where: { slug: 'web-development' },
      update: {},
      create: { name: 'Web Development', slug: 'web-development' }
    })
  ])

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 12)
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'admin',
      password: hashedPassword,
      role: 'ADMIN',
      firstName: 'Admin',
      lastName: 'User',
      bio: 'Platform administrator and content curator',
      profilePublic: true,
      emailNotifications: true
    }
  })

  // Create sample author
  const authorPassword = await bcrypt.hash('author123', 12)
  const authorUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'johndoe',
      password: authorPassword,
      role: 'AUTHOR',
      firstName: 'John',
      lastName: 'Doe',
      bio: 'Passionate writer and technology enthusiast. Love sharing insights about web development and design.',
      profilePublic: true,
      emailNotifications: true,
      website: 'https://johndoe.dev',
      twitterUrl: 'https://twitter.com/johndoe',
      linkedinUrl: 'https://linkedin.com/in/johndoe'
    }
  })

  console.log('✅ Database seeded successfully!')
  console.log('👤 Admin user: <EMAIL> / admin123')
  console.log('✍️ Author user: <EMAIL> / author123')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })

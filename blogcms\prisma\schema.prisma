// BlogCMS - Comprehensive Database Schema
// Built for scalability and performance

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  role      UserRole @default(READER)

  // Profile Information
  firstName String?
  lastName  String?
  bio       String?
  avatarUrl String?
  website   String?
  location  String?

  // Social Links
  twitterUrl   String?
  linkedinUrl  String?
  githubUrl    String?

  // Settings
  emailNotifications Boolean @default(true)
  profilePublic      Boolean @default(true)

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  lastLoginAt DateTime?

  // Relations
  articles     Article[]
  comments     Comment[]
  reactions    Reaction[]
  bookmarks    Bookmark[]
  followers    Follow[] @relation("UserFollowers")
  following    Follow[] @relation("UserFollowing")
  notifications Notification[]

  // NextAuth.js relations
  accounts Account[]
  sessions Session[]

  @@map("users")
}

// NextAuth.js Account model
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

// NextAuth.js Session model
model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// NextAuth.js VerificationToken model
model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// Content Management
model Article {
  id          String        @id @default(cuid())
  title       String
  subtitle    String?
  slug        String        @unique
  content     String
  excerpt     String?

  // Media
  featuredImageUrl String?
  featuredImageAlt String?

  // Publishing
  status           ArticleStatus @default(DRAFT)
  publishedAt      DateTime?
  scheduledAt      DateTime?

  // Metrics
  viewCount        Int @default(0)
  readingTime      Int @default(0) // in minutes

  // SEO
  metaDescription  String?
  ogTitle          String?
  ogDescription    String?
  ogImageUrl       String?

  // Features
  isFeatured       Boolean @default(false)
  allowComments    Boolean @default(true)

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  authorId     String
  author       User       @relation(fields: [authorId], references: [id], onDelete: Cascade)
  categoryId   String?
  category     Category?  @relation(fields: [categoryId], references: [id])
  tags         ArticleTag[]
  comments     Comment[]
  reactions    Reaction[]
  bookmarks    Bookmark[]
  versions     ArticleVersion[]

  @@map("articles")
}

// Article Version History
model ArticleVersion {
  id        String   @id @default(cuid())
  title     String
  content   String
  version   Int
  createdAt DateTime @default(now())

  articleId String
  article   Article @relation(fields: [articleId], references: [id], onDelete: Cascade)

  @@unique([articleId, version])
  @@map("article_versions")
}

// Categories
model Category {
  id          String @id @default(cuid())
  name        String @unique
  slug        String @unique
  description String?
  color       String? // Hex color for UI

  // Metrics
  articleCount Int @default(0)

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  articles Article[]

  @@map("categories")
}

// Tags
model Tag {
  id          String @id @default(cuid())
  name        String @unique
  slug        String @unique
  description String?

  // Metrics
  usageCount Int @default(0)

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  articles ArticleTag[]

  @@map("tags")
}

// Many-to-many relationship between Articles and Tags
model ArticleTag {
  articleId String
  tagId     String

  article Article @relation(fields: [articleId], references: [id], onDelete: Cascade)
  tag     Tag     @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([articleId, tagId])
  @@map("article_tags")
}

// Comments System
model Comment {
  id      String @id @default(cuid())
  content String

  // Hierarchy for nested comments
  parentId String?
  parent   Comment?  @relation("CommentReplies", fields: [parentId], references: [id])
  replies  Comment[] @relation("CommentReplies")

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  authorId  String
  author    User    @relation(fields: [authorId], references: [id], onDelete: Cascade)
  articleId String
  article   Article @relation(fields: [articleId], references: [id], onDelete: Cascade)

  @@map("comments")
}

// Reactions (likes, claps, etc.)
model Reaction {
  id   String      @id @default(cuid())
  type ReactionType

  // Timestamps
  createdAt DateTime @default(now())

  // Relations
  userId    String
  user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  articleId String
  article   Article @relation(fields: [articleId], references: [id], onDelete: Cascade)

  @@unique([userId, articleId, type])
  @@map("reactions")
}

// Bookmarks
model Bookmark {
  id String @id @default(cuid())

  // Timestamps
  createdAt DateTime @default(now())

  // Relations
  userId    String
  user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  articleId String
  article   Article @relation(fields: [articleId], references: [id], onDelete: Cascade)

  @@unique([userId, articleId])
  @@map("bookmarks")
}

// Follow System
model Follow {
  id String @id @default(cuid())

  // Timestamps
  createdAt DateTime @default(now())

  // Relations
  followerId  String
  follower    User @relation("UserFollowing", fields: [followerId], references: [id], onDelete: Cascade)
  followingId String
  following   User @relation("UserFollowers", fields: [followingId], references: [id], onDelete: Cascade)

  @@unique([followerId, followingId])
  @@map("follows")
}

// Notifications System
model Notification {
  id      String           @id @default(cuid())
  type    NotificationType
  title   String
  message String
  read    Boolean          @default(false)

  // Optional data for different notification types
  data Json?

  // Timestamps
  createdAt DateTime @default(now())

  // Relations
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

// Analytics for articles
model ArticleView {
  id        String   @id @default(cuid())
  ipAddress String?
  userAgent String?
  country   String?

  // Timestamps
  viewedAt DateTime @default(now())

  // Relations
  articleId String
  userId    String? // null for anonymous views

  @@map("article_views")
}

// Enums
enum UserRole {
  ADMIN
  EDITOR
  AUTHOR
  READER
}

enum ArticleStatus {
  DRAFT
  REVIEW
  PUBLISHED
  ARCHIVED
}

enum ReactionType {
  LIKE
  CLAP
  LOVE
  DISLIKE
}

enum NotificationType {
  NEW_FOLLOWER
  NEW_COMMENT
  NEW_REACTION
  ARTICLE_PUBLISHED
  MENTION
  SYSTEM
}

// Indexes for performance
// These will be added as separate migration files for better control

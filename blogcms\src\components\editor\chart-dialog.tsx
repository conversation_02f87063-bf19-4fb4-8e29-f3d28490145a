'use client'

import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TrendingUp } from 'lucide-react'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Cell, Pie } from 'recharts'

interface ChartDialogProps {
  isOpen: boolean
  onClose: () => void
  onInsert: (chartHtml: string) => void
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8']

const sampleData = [
  { name: 'Jan', value: 400, sales: 240 },
  { name: 'Feb', value: 300, sales: 139 },
  { name: 'Mar', value: 200, sales: 980 },
  { name: 'Apr', value: 278, sales: 390 },
  { name: 'May', value: 189, sales: 480 },
  { name: 'Jun', value: 239, sales: 380 },
]

const pieData = [
  { name: '<PERSON>ktop', value: 45 },
  { name: 'Mobile', value: 35 },
  { name: 'Tablet', value: 20 },
]

export function ChartDialog({ isOpen, onClose, onInsert }: ChartDialogProps) {
  const [chartType, setChartType] = useState<'bar' | 'line' | 'pie' | 'area'>('bar')
  const [chartData, setChartData] = useState(JSON.stringify(sampleData, null, 2))
  const [chartTitle, setChartTitle] = useState('Sample Chart')

  const handleInsert = () => {
    try {
      const data = JSON.parse(chartData)
      
      // Create a unique ID for the chart
      const chartId = `chart-${Date.now()}`
      
      // Generate the chart HTML with embedded data
      const chartHtml = `
        <div class="chart-container my-6 p-4 border rounded-lg bg-gray-50" data-chart-type="${chartType}" data-chart-data='${JSON.stringify(data)}' data-chart-title="${chartTitle}">
          <h4 class="text-lg font-semibold mb-4 text-center">${chartTitle}</h4>
          <div class="h-64 flex items-center justify-center bg-white rounded border">
            <div class="text-gray-500">
              <${chartType === 'bar' ? 'BarChart3' : chartType === 'line' ? 'LineChart' : chartType === 'pie' ? 'PieChart' : 'TrendingUp'} class="h-12 w-12 mx-auto mb-2" />
              <p>${chartTitle}</p>
              <p class="text-sm">Interactive chart will render here</p>
            </div>
          </div>
        </div>
      `
      
      onInsert(chartHtml)
      onClose()
    } catch (error) {
      alert('Invalid JSON data. Please check your chart data format.')
    }
  }

  const renderPreview = () => {
    try {
      const data = JSON.parse(chartData)
      
      switch (chartType) {
        case 'bar':
          return (
            <ResponsiveContainer width="100%" height={200}>
              <BarChart data={data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="value" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          )
        case 'line':
          return (
            <ResponsiveContainer width="100%" height={200}>
              <RechartsLineChart data={data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="value" stroke="#8884d8" strokeWidth={2} />
              </RechartsLineChart>
            </ResponsiveContainer>
          )
        case 'pie':
          const pieDataToUse = data.length > 0 && 'name' in data[0] && 'value' in data[0] ? data : pieData
          return (
            <ResponsiveContainer width="100%" height={200}>
              <RechartsPieChart>
                <Pie
                  data={pieDataToUse}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={60}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {pieDataToUse.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </RechartsPieChart>
            </ResponsiveContainer>
          )
        default:
          return <div className="h-48 flex items-center justify-center text-gray-500">Preview not available</div>
      }
    } catch (error) {
      return <div className="h-48 flex items-center justify-center text-red-500">Invalid data format</div>
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Insert Chart</h3>
          <button
            type="button"
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Configuration */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Chart Title
              </label>
              <input
                type="text"
                value={chartTitle}
                onChange={(e) => setChartTitle(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Chart Type
              </label>
              <div className="grid grid-cols-2 gap-2">
                <button
                  type="button"
                  onClick={() => setChartType('bar')}
                  className={`p-3 border rounded-lg flex flex-col items-center gap-2 ${
                    chartType === 'bar' ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
                  }`}
                >
                  <BarChart3 className="h-6 w-6" />
                  <span className="text-sm">Bar Chart</span>
                </button>
                <button
                  type="button"
                  onClick={() => setChartType('line')}
                  className={`p-3 border rounded-lg flex flex-col items-center gap-2 ${
                    chartType === 'line' ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
                  }`}
                >
                  <LineChart className="h-6 w-6" />
                  <span className="text-sm">Line Chart</span>
                </button>
                <button
                  type="button"
                  onClick={() => setChartType('pie')}
                  className={`p-3 border rounded-lg flex flex-col items-center gap-2 ${
                    chartType === 'pie' ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
                  }`}
                >
                  <PieChart className="h-6 w-6" />
                  <span className="text-sm">Pie Chart</span>
                </button>
                <button
                  type="button"
                  onClick={() => setChartType('area')}
                  className={`p-3 border rounded-lg flex flex-col items-center gap-2 ${
                    chartType === 'area' ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
                  }`}
                >
                  <TrendingUp className="h-6 w-6" />
                  <span className="text-sm">Area Chart</span>
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Chart Data (JSON)
              </label>
              <textarea
                value={chartData}
                onChange={(e) => setChartData(e.target.value)}
                rows={8}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                placeholder="Enter your chart data in JSON format..."
              />
              <p className="text-xs text-gray-500 mt-1">
                Format: [{"name": "Label", "value": 123}, ...]
              </p>
            </div>

            <button
              type="button"
              onClick={handleInsert}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
            >
              Insert Chart
            </button>
          </div>

          {/* Preview */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Preview</h4>
            <div className="border rounded-lg p-4 bg-gray-50">
              <h5 className="text-center font-medium mb-4">{chartTitle}</h5>
              {renderPreview()}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

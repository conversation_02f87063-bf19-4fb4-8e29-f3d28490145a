import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/dashboard/stats - Get user dashboard statistics
export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get user's article statistics
    const [
      totalArticles,
      publishedArticles,
      draftArticles,
      totalViews,
      totalComments,
      totalReactions
    ] = await Promise.all([
      // Total articles
      prisma.article.count({
        where: { authorId: session.user.id }
      }),
      
      // Published articles
      prisma.article.count({
        where: { 
          authorId: session.user.id,
          status: 'PUBLISHED'
        }
      }),
      
      // Draft articles
      prisma.article.count({
        where: { 
          authorId: session.user.id,
          status: 'DRAFT'
        }
      }),
      
      // Total views across all articles
      prisma.article.aggregate({
        where: { authorId: session.user.id },
        _sum: { viewCount: true }
      }),
      
      // Total comments on user's articles
      prisma.comment.count({
        where: {
          article: {
            authorId: session.user.id
          }
        }
      }),
      
      // Total reactions on user's articles
      prisma.reaction.count({
        where: {
          article: {
            authorId: session.user.id
          }
        }
      })
    ])

    const stats = {
      totalArticles,
      publishedArticles,
      draftArticles,
      totalViews: totalViews._sum.viewCount || 0,
      totalComments,
      totalReactions
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching dashboard stats:', error)
    return NextResponse.json(
      { error: 'Failed to fetch dashboard statistics' },
      { status: 500 }
    )
  }
}
